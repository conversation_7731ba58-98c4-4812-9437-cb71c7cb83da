import 'firebase/firestore';
import firestore, { DocumentReference, Timestamp } from 'firebase/firestore';
import {
  EMessageContext,
  EMessageDirection,
  IFirestoreMessageEntity,
  TStatuses,
} from './message-entities-types';
import { IClientEntity } from './client-entity-types';

export interface IChatRoomEntityFields {
  ref: DocumentReference;
  blocked: boolean;
  blockReason: string | null;
  clients: DocumentReference[];
  contacts: string[];
  created_at: Timestamp;
  doc_department?: DocumentReference | null;
  cityGroup?: string | null;
  cityGroupUpdatedAt?: Timestamp | null;
  cityGroupUpdatedBy?: string | null;
  organization?: string | null;
  organizationUpdatedAt?: Timestamp | null;
  organizationUpdatedBy?: string | null;
  organizationGroup?: string | null;
  dream_vehicle?: IClientEntity['dream_vehicle'] | null;
  headers: IRoomHeader;
  label?: DocumentReference | null;
  label_updated_at?: Timestamp | null;
  last_inbound?: Timestamp | null;
  last_message_type: EMessageDirection;
  recent_chat: IRecentChatEntity;
  wait_for_answer?: {
    asked_at: Timestamp | null;
    message_ref: DocumentReference;
    question_id: null;
    topic: string;
  } | null;
  exclusive_admin?: {
    email: string;
    ref: string;
    name: string;
  } | null;
  agent_ai_reply?: boolean;
  pinnedMessages?: {
    message: IFirestoreMessageEntity['message'];
    statuses: IFirestoreMessageEntity['statuses'];
    origin: IFirestoreMessageEntity['origin'];
    pinnedAt: Timestamp;
  }[]
}

export interface IChatRoomEntity extends IChatRoomEntityFields {}

export interface IRecentChatEntity {
  contact: string;
  direction: EMessageDirection;
  display_name: string;
  read: boolean;
  statuses?: TStatuses;
  text: string;
  timestamp: Timestamp;
  type: EMessageContext;
  unixtime: number;
}

export interface IRoomHeader {
  title: string;
}
