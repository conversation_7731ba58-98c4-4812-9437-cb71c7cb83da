import React from 'react';
import { Icon } from 'semantic-ui-react';
import { mainApiServices } from '../../../../services/MainApiServices';
import ChatContentWrapper from './ChatContentWrapper';
import MessageEntity from '../../../../entities/MessageEntity';

interface ChatItemDocumentProps {
  message: MessageEntity;
  isOutbound?: boolean;
}

const ChatItemDocument: React.FC<ChatItemDocumentProps> = ({ message, isOutbound = false }) => {
  if (message.message.type !== 'document' || !message.message.document) {
    return null;
  }

  const document = message.message.document;
  const url =
    message.message.direction === 'IN'
      ? mainApiServices.getMediaUrl(message.ref, document.filename || 'file.pdf')
      : document.link;

  return (
    <ChatContentWrapper
      icon="file pdf outline"
      isOutbound={isOutbound}
      action={
        <a
          href={url}
          target="_blank"
          rel="noreferrer"
          className={`${
            isOutbound ? 'text-blue-600 hover:text-blue-800' : 'text-blue-600 hover:text-blue-800'
          } text-sm inline-flex items-center gap-2`}
        >
          <Icon
            name="external"
            size="small"
          />
          Buka dokumen
        </a>
      }
    >
      <div className={`text-sm ${isOutbound ? 'text-gray-700' : 'text-gray-700'}`}>
        {document.filename || 'File Dokumen'}
      </div>
    </ChatContentWrapper>
  );
};

export default ChatItemDocument;
