import React, { Component } from 'react';
import { Dropdown, Message } from 'semantic-ui-react';
import { IChatItemProps, IChatItemStates } from '../types/chat-item-types';
import moment from 'moment';
import isSameDay from '../../../helpers/isSameday';
import HTMLReactParser, { Element as ElementHtmlReactParser } from 'html-react-parser';
import { connect } from 'react-redux';
import { TMainReduxStates } from '../../../redux/types/redux-types';
import wrapLinkAnchor from '../../../helpers/wrapLinkAnchor';
import { appendHtmlToPhoneNumber } from '../../../helpers/maskPhoneNumber/appendHtmlToPhoneNumber.prototype.ver2';
import checkTextContainsPhoneNumber from '../../../helpers/maskPhoneNumber/checkTextContainsPhoneNumber';
import ChatItemImagePreview from './components/ChatItemImagePreview';
import ChatItemContact from './components/ChatItemContact';
import ChatItemDocument from './components/ChatItemDocument';
import ChatItemLocation from './components/ChatItemLocation';
import ChatItemSourceAd from './components/ChatItemSourceAd';
import ChatItemVideo from './components/ChatItemVideo';
import ChatItemUnsupported from './components/ChatItemUnsupported';
import { mainStore } from '../../../redux/reducers';
import modalSetPhoneNumberAsSlice from '../../../redux/modal-update-phone-number/modalSetPhoneNumberAs.slice';
import modalChatDebugSlice from '../../../redux/modal-chat-debug/modalChatDebug.slice';
import { BsCheck, BsCheckAll, BsThreeDotsVertical, BsPinFill } from 'react-icons/bs';
import ChatItemReplyContext from './components/ChatItemReplyContext';
import { mainApiServices } from '../../../services/MainApiServices';

class ChatItem extends Component<IChatItemProps, IChatItemStates> {
  private longPressTimer: NodeJS.Timeout | null = null;
  private longPressDelay = 500; // 500ms for long press

  // Constructor: Instantiate ChatItem component with initial props.
  constructor(props: IChatItemProps) {
    super(props);
    this.state = {
      imageErrorFetch: false,
      dropdownOpen: false,
      isHovered: false,
    };
  }

  // Function: dateRender
  // Purpose: Convert and format the unix timestamp to a readable date/time.
  //          Returns "HH:mm" if the message is from today, otherwise "YYYY-MM-DD HH:mm".
  dateRender = () => {
    // Convert unix time to moment instance
    const chatMoment = moment.unix(this.props.message.message.unixtime);
    // Return time if message is from today; else, date & time.
    return isSameDay(chatMoment)
      ? chatMoment.format('HH:mm')
      : chatMoment.format('YYYY/MM/DD • HH:mm');
  };

  // Function: textBody
  // Purpose: Extract the text content from the message based on its type.
  //          Handles document, image, video, text, button, and interactive types.
  textBody = () => {
    // Destructure message content from props
    const { message: m } = this.props.message;
    let textBody: string = '';

    // Determine text content based on message type
    switch (m.type) {
      case 'document':
        textBody = m.document?.caption ?? '';
        break;
      case 'image':
        textBody = m.image?.caption ?? '';
        break;
      case 'video':
        textBody = m.video?.caption ?? '';
        break;
      case 'text':
        textBody = m.text?.body ?? '';
        break;
      case 'button':
        textBody = m.button?.text ?? '';
        break;
      case 'interactive':
        // Handle interactive message type: button or list reply.
        if (m.interactive?.type === 'button_reply') {
          textBody = m.interactive.button_reply?.title ?? '';
        } else if (m.interactive?.type === 'list_reply') {
          textBody = m.interactive.list_reply?.title ?? '';
        }
        break;
    }
    return textBody;
  };

  // Function: outboundTextRender
  // Purpose: Render text content for outbound (sent) messages.
  //          It extracts the relevant text/caption and, if applicable, a header.
  outboundTextRender = () => {
    // Destructure message content from props
    const { message: m } = this.props.message;
    let textBody: string = '';
    let textHeader: string = '';

    // Determine content based on message type.
    switch (m.type) {
      case 'image':
        textBody = m.image?.caption ?? '';
        break;
      case 'video':
        textBody = m.video?.caption ?? '';
        break;
      case 'document':
        textBody = m.document?.caption ?? '';
        break;
      case 'text':
        textBody = m.text?.body ?? '';
        break;
      case 'interactive':
        textBody = m.interactive?.body?.text ?? '';
        textHeader = m.interactive?.header?.text ?? '';
        break;
      default:
        break;
    }

    // Parse HTML content to safely render any embedded HTML elements.
    const parser = HTMLReactParser(textBody);

    return (
      <div
        // Container style: ensures proper text wrapping and scrolling behavior.
        style={{
          maxWidth: '100%',
          overflowX: 'auto',
          wordBreak: 'break-word',
          whiteSpace: 'pre-wrap',
        }}
      >
        {!!textHeader && (
          <>
            <strong>{textHeader}</strong>
            <br />
          </>
        )}
        <div className="whitespace-pre-line">{textBody && parser}</div>
      </div>
    );
  };

  // Function: infoHidePhoneNumber
  // Purpose: Render an informational message if the phone number is hidden.
  //          This applies to incoming messages when the admin rank is not 1.
  infoHidePhoneNumber = () => {
    if (this.props.message.message.direction === 'IN' && this.props.admin.admin?.admin_rank !== 1) {
      const isContainPhoneNumber = checkTextContainsPhoneNumber(this.textBody());

      if (isContainPhoneNumber)
        return <div className={'mt-2 text-gray-400'}>Nomor telepon kami sembunyikan</div>;
    }
  };

  // Function: inboundTextRender
  // Purpose: Render inbound (received) text messages with proper phone number masking.
  //          It applies HTML formatting for phone numbers and wraps links accordingly.
  inboundTextRender = () => {
    const { admin } = this.props;
    let textBody: string = this.textBody();

    // Determine masking based on admin rank: if admin rank is not 1, mask the phone number.
    const maskPhoneNumber = admin.admin?.admin_rank !== 1;

    // Append HTML for phone numbers and wrap links if necessary.
    textBody = appendHtmlToPhoneNumber(textBody, { mask: maskPhoneNumber });
    textBody = wrapLinkAnchor(textBody);

    // Parse HTML and replace span elements for masked phone numbers with clickable spans.
    const parser = HTMLReactParser(textBody, {
      replace: (domNode: any) => {
        if (domNode instanceof ElementHtmlReactParser) {
          if (domNode.name === 'span' && domNode.attribs?.class === 'mask-phone-number') {
            const masked = (domNode.children[0] as any)?.data;
            const sanitized = domNode.attribs?.sanitized ?? '';
            return (
              <span
                className="underline cursor-pointer text-blue-600"
                onClick={() => {
                  mainStore.dispatch(
                    modalSetPhoneNumberAsSlice.actions.open({
                      phoneNumber: sanitized,
                    }),
                  );
                }}
              >
                {masked}
              </span>
            );
          }
        }
      },
    });

    // If no text content is available, return null.
    if (!textBody) {
      return null;
    }

    return (
      <div
        className="whitespace-pre-line"
        style={{
          maxWidth: '100%',
          overflowX: 'auto',
          wordBreak: 'break-word',
          whiteSpace: 'pre-wrap',
        }}
      >
        {parser}
      </div>
    );
  };

  // Function: renderStatuses
  // Purpose: Render the status icon (sent, delivered, read) for the message.
  renderStatuses = (): JSX.Element => {
    // Destructure the message statuses.
    const { sent, read, delivered } = this.props.message.statuses!;

    if (read) {
      return (
        <div className="flex items-center text-green-500">
          <BsCheckAll size={16} />
        </div>
      );
    } else if (delivered) {
      return (
        <div className="flex items-center text-blue-500">
          <BsCheckAll size={16} />
        </div>
      );
    } else if (sent) {
      return (
        <div className="flex items-center text-gray-500">
          <BsCheck size={16} />
        </div>
      );
    }

    // Default: return an empty div if no status applies.
    return <div />;
  };

  // Function: handleDropdownToggle
  // Purpose: Toggle the dropdown menu visibility
  handleDropdownToggle = () => {
    this.setState((prevState) => ({
      dropdownOpen: !prevState.dropdownOpen,
    }));
  };

  // Function: handleDropdownClose
  // Purpose: Close the dropdown menu
  handleDropdownClose = () => {
    this.setState({ dropdownOpen: false });
  };

  // Function: handleMouseEnter
  // Purpose: Handle mouse enter for hover effect on desktop
  handleMouseEnter = () => {
    this.setState({ isHovered: true });
  };

  // Function: handleMouseLeave
  // Purpose: Handle mouse leave for hover effect on desktop
  handleMouseLeave = () => {
    this.setState({ isHovered: false, dropdownOpen: false });
  };

  // Function: handleReplyClick
  // Purpose: Handle reply button click (currently disabled)
  handleReplyClick = () => {
    // TODO: Implement reply functionality
    console.log('Reply clicked for message:', this.props.message.message.id);
  };

  // Function: handleDebugClick
  // Purpose: Handle debug button click
  handleDebugClick = () => {
    mainStore.dispatch(modalChatDebugSlice.actions.open(this.props.message));
    this.setState({ dropdownOpen: false });
  };

  // Function: isPinned
  // Purpose: Check if the message is pinned
  isPinned = () => {
    return this.props.conversation.chatRoom?.pinnedMessages?.some(
      (m) => m.message.id === this.props.message.message.id,
    );
  };

  // Function: handlePinMessageClick
  // Purpose: Handle pin message button click
  handlePinMessageClick = () => {
    mainApiServices.pinMessage({
      mode: this.isPinned() ? 'unpin' : 'pin',
      chatRoomPath: this.props.conversation.chatRoom!.ref.path,
      messagePath: this.props.message.ref.path,
    }).catch((e) => {
      console.error('Error pin message: ', e);
    });
    this.setState({ dropdownOpen: false });
  };

  // Function: handleTouchStart
  // Purpose: Handle touch start for long press detection on mobile
  handleTouchStart = (e: React.TouchEvent) => {
    if (this.isMobileDevice()) {
      this.longPressTimer = setTimeout(() => {
        this.setState({ dropdownOpen: true });
        // Prevent context menu on long press
        e.preventDefault();
      }, this.longPressDelay);
    }
  };

  // Function: handleTouchEnd
  // Purpose: Handle touch end to clear long press timer
  handleTouchEnd = () => {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  };

  // Function: handleTouchMove
  // Purpose: Handle touch move to cancel long press if user moves finger
  handleTouchMove = () => {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  };

  // Function: componentWillUnmount
  // Purpose: Cleanup timers when component unmounts
  componentWillUnmount() {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
    }
  }

  // Function: isMobileDevice
  // Purpose: Detect if the current device is mobile
  isMobileDevice = () => {
    return window.innerWidth <= 768 || 'ontouchstart' in window;
  };

  // Function: renderPinIndicator
  // Purpose: Render the pin indicator for pinned messages
  renderPinIndicator = (isIncoming: boolean) => {
    if (!this.isPinned()) return null;

    return (
      <div
        className={`
          absolute z-10 flex items-center justify-center w-6 h-6 rounded-full
          ${isIncoming ? 'top-1 right-1' : 'top-1 left-1'}
          bg-yellow-400 text-yellow-800 shadow-sm
        `}
        title="Pesan yang di-pin"
        aria-label="Pesan yang di-pin"
      >
        <BsPinFill size={10} />
      </div>
    );
  };

  // Function: renderDropdownMenu
  // Purpose: Render the dropdown menu with Reply and Debug options
  renderDropdownMenu = (isIncoming: boolean) => {
    const { dropdownOpen, isHovered } = this.state;
    const isMobile = this.isMobileDevice();

    // On desktop, only show when hovered or dropdown is open
    // On mobile, always show the trigger
    const shouldShowTrigger = isMobile || isHovered || dropdownOpen;

    if (!shouldShowTrigger) return null;

    return (
      <div
        className={`
          chat-item-dropdown absolute
          ${isIncoming ? 'top-2 left-2' : 'top-2 right-2'}
        `}
      >
        <Dropdown
          open={dropdownOpen}
          onClose={this.handleDropdownClose}
          icon={null}
          trigger={
            <button
              onClick={this.handleDropdownToggle}
              className={`
                flex items-center justify-center w-7 h-7 rounded-full transition-all duration-200
                focus:outline-none focus:ring-2 focus:ring-offset-1
                ${
                  isIncoming
                    ? 'bg-white/90 hover:bg-white text-gray-600 hover:text-gray-800 focus:ring-gray-300'
                    : 'bg-blue-600/90 hover:bg-blue-600 text-white focus:ring-blue-300'
                }
                ${isMobile ? 'opacity-80' : 'opacity-70 hover:opacity-100'}
                shadow-sm hover:shadow-md backdrop-blur-sm
              `}
              title="Message options"
              aria-label="Message options"
            >
              <BsThreeDotsVertical size={12} />
            </button>
          }
          pointing={isIncoming ? 'top left' : 'top right'}
          direction={isIncoming ? 'right' : 'left'}
          className="chat-item-dropdown"
        >
          <Dropdown.Menu>
            <Dropdown.Item
              onClick={this.handleReplyClick}
              disabled={true}
              className="disabled"
            >
              <div className="flex items-center justify-between">
                <span className="text-sm">Reply</span>
                <span className="text-xs opacity-60">(Coming Soon)</span>
              </div>
            </Dropdown.Item>
            <Dropdown.Item onClick={this.handleDebugClick}>
              <div className="flex items-center">
                <span className="text-sm">Debug</span>
              </div>
            </Dropdown.Item>
            <Dropdown.Item onClick={this.handlePinMessageClick}>
              <div className="flex items-center">
                <span className="text-sm">
                  {this.isPinned() ? 'Unpin Message' : 'Pin Message'}
                </span>
              </div>
            </Dropdown.Item>
          </Dropdown.Menu>
        </Dropdown>
      </div>
    );
  };

  // Function: render
  // Purpose: Main render function that constructs the chat item UI based on the message data.
  render() {
    // Determine if the message is incoming or outgoing.
    const isIncoming = this.props.message.message.direction === 'IN';
    // Check if there is an error associated with the message.
    const hasError = this.props.message.error;
    // Create a moment instance for the message's timestamp.
    const chatMomentInstance = moment.unix(this.props.message.message.unixtime);
    // Determine if the message was sent today.
    const isToday = isSameDay(chatMomentInstance);

    return (
      <div
        id={`chat-item-row-${this.props.message.message.id}`}
        className={`flex ${isIncoming ? 'justify-start' : 'justify-end'} mb-2 px-4 py-1`}
      >
        <div className={`max-w-[70%] ${isIncoming ? 'order-2' : 'order-1'}`}>
          <div
            id={`message-${this.props.message.message.id}`}
            className={`
							relative rounded-2xl px-4 py-3 shadow-sm transition-all duration-200
							${isIncoming ? 'bg-white border border-gray-200' : 'bg-blue-50 border border-blue-200'}
						`}
            onMouseEnter={this.handleMouseEnter}
            onMouseLeave={this.handleMouseLeave}
            onTouchStart={this.handleTouchStart}
            onTouchEnd={this.handleTouchEnd}
            onTouchMove={this.handleTouchMove}
          >
            {/* Render reply context if available */}
            {this.props.message.message.context && (
              <ChatItemReplyContext context={this.props.message.message.context} />
            )}

            {/* Pin Indicator */}
            {this.renderPinIndicator(isIncoming)}

            {/* Dropdown Menu */}
            {this.renderDropdownMenu(isIncoming)}
            {/* Render context-based components for incoming messages */}
            {isIncoming && <ChatItemSourceAd message={this.props.message} />}
            {isIncoming && <ChatItemUnsupported message={this.props.message} />}
            {isIncoming && <ChatItemContact message={this.props.message} />}
            {isIncoming && <ChatItemLocation message={this.props.message} />}

            {/* Render document and video message components */}
            <ChatItemDocument
              message={this.props.message}
              isOutbound={!isIncoming}
            />
            <ChatItemVideo
              message={this.props.message}
              isOutbound={!isIncoming}
            />

            {/* Render image preview if the message includes an image */}
            {this.props.message.message.image && (
              <ChatItemImagePreview message={this.props.message} />
            )}

            {/* Render the textual content based on message direction */}
            <div className={`break-words ${!isIncoming ? 'text-gray-700' : 'text-gray-600'}`}>
              {isIncoming ? this.inboundTextRender() : this.outboundTextRender()}
              {this.infoHidePhoneNumber()}
            </div>

            {/* Render error message if one exists */}
            {hasError && (
              <div className="mt-2">
                <Message
                  error
                  size="mini"
                  className={`!m-0 !p-2 ${
                    !isIncoming
                      ? '!bg-red-50 !text-red-600 !border-red-100'
                      : '!bg-red-50/50 !text-red-600 !border-red-100'
                  }`}
                  content={this.props.message.error?.title || 'Gambar gagal dimuat'}
                />
              </div>
            )}

            {/* Render sender info for outbound messages */}
            {!isIncoming && !this.props.message.statuses?.failed && (
              <div className="mt-1 text-sm text-gray-500">
                {this.props.message.origin.display_name}
              </div>
            )}
          </div>

          {/* Render the message status and the formatted date/time */}
          <div
            className={`
							flex items-center gap-2 mt-1 text-xs
							${isIncoming ? 'text-gray-500' : 'text-gray-400 justify-end'}
						`}
          >
            {!isIncoming && !this.props.message.statuses?.failed && (
              <div className="flex items-center">{this.renderStatuses()}</div>
            )}
            {/* If the message is not from today, display date in a separate block */}
            <span className={!isToday ? 'block' : ''}>{this.dateRender()}</span>
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = (state: TMainReduxStates) => {
  return {
    admin: state.reducerAdmin,
    conversation: state.reducerConversation,
  };
};

export default connect(mapStateToProps)(ChatItem);
