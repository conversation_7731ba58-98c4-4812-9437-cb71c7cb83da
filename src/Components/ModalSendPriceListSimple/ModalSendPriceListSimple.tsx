import React, { Component, SyntheticEvent } from 'react';
import {
  Button,
  DropdownProps,
  Form,
  Icon,
  Input,
  Message,
  Modal,
  Radio,
  Segment,
  Select,
} from 'semantic-ui-react';
import { TMainReduxStates } from '../../redux/types/redux-types';
import { Area, Model, VariantProduct } from '../../services/types/catalaogueTypes';
import SelectAreaFromCatalog from '../SelectCityGroup/SelectAreaFromCatalog';
import { mainStore } from '../../redux/reducers';
import { catalogueServices } from '../../services/catalogue/catalogueServices';
import { connect } from 'react-redux';
import modalPriceListSliceSimple, {
  fetchPriceList,
} from '../../redux/modal-price-list-simple/modalPriceListSliceSimple';
import PriceListTable from './PriceListTable';
import collect from 'collect.js';
import currencyFormat from '../../helpers/currencyFormat';
import { mainApiServices } from '../../services/MainApiServices';
import { IPromoCode } from '../../services/types/promoServiceTypes';
import { promoService } from '../../services/promo/promoServices';
import { CheckboxProps } from 'semantic-ui-react/dist/commonjs/modules/Checkbox/Checkbox';
import { InputOnChangeData } from 'semantic-ui-react/dist/commonjs/elements/Input/Input';
import { Timestamp, updateDoc } from 'firebase/firestore';

interface Props {
  conversation: TMainReduxStates['reducerConversation'];
  admin: TMainReduxStates['reducerAdmin'];
  customer: TMainReduxStates['customerReducer'];
  priceList: TMainReduxStates['modalSendPriceListSimple'];
  project: TMainReduxStates['reducerProject'];
}

interface States {
  fetchingArea: boolean;
  fetchingModel: boolean;

  areas: Area[];
  models: Model[];
  variants: VariantProduct[];

  selectedCityGroup: string | null;
  selectedModel: Model | null;
  selectedVariant: VariantProduct | null;
  sending: boolean;
  errorMessage: string;

  availablePromoCodes: IPromoCode[];
}

class ModalSendPriceListSimple extends Component<Props, States> {
  constructor(props: Props) {
    super(props);

    this.state = {
      fetchingArea: false,
      fetchingModel: false,

      areas: [],
      models: [],
      variants: [],
      selectedCityGroup: null,
      selectedModel: null,
      selectedVariant: null,
      sending: false,
      errorMessage: '',

      availablePromoCodes: [],
    };
  }

  onCancel = () => {
    mainStore.dispatch(modalPriceListSliceSimple.actions.close());
  };

  private selectArea = {
    onSelect: (cityGroup: string) => {
      mainStore.dispatch(modalPriceListSliceSimple.actions.reset());
      this.setState(
        {
          selectedCityGroup: cityGroup || null,
          selectedModel: null,
          models: [],
          variants: [],
        },
        () => {
          this.selectModel.fetchModel().catch();
        },
      );
    },
  };

  private selectVariant = {
    fetchVariant: async () => {
      await new Promise((resolve) => {
        this.setState(
          {
            variants: [],
            selectedVariant: null,
          },
          async () => {
            let currentState: States = { ...this.state };
            const getVariants = await catalogueServices.getVariantByAreaAMH({
              modelName: this.state.selectedModel?.model_name ?? '',
              area: this.state.selectedCityGroup ?? '',
            });
            currentState.variants = (getVariants?.data || []) as VariantProduct[];
            this.setState(
              {
                ...currentState,
              },
              () => resolve(true),
            );
          },
        );
      });
    },
    onSelect: (event: SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      const variant =
        this.state.variants.find((value) => value.variant_code === data.value) || null;
      mainStore.dispatch(modalPriceListSliceSimple.actions.reset());
      mainStore.dispatch(
        modalPriceListSliceSimple.actions.setVehicle({
          cityGroup: this.state.selectedCityGroup!,
          variant: variant!,
        }),
      );
      this.setState(
        {
          selectedVariant: variant,
          availablePromoCodes: [],
        },
        () => {
          this.fetchPriceList();
        },
      );
    },
  };

  private selectModel = {
    fetchModel: () => {
      return new Promise((resolve) => {
        this.setState(
          {
            fetchingModel: true,
            models: [],
            selectedModel: null,
          },
          async () => {
            let currentState: States = { ...this.state };
            const fetchModel = await catalogueServices.getModelByCityGroup({
              area: this.state.selectedCityGroup ?? '',
            });

            currentState.models = fetchModel?.data ?? [];
            currentState.fetchingModel = false;

            this.setState({ ...currentState }, () => resolve(true));
          },
        );
      });
    },
    onSelect: (event: SyntheticEvent<HTMLElement>, data: DropdownProps) => {
      mainStore.dispatch(modalPriceListSliceSimple.actions.reset());
      this.setState(
        {
          selectedModel: this.state.models.find((value) => value.model_name === data.value) ?? null,
        },
        () => {
          this.selectVariant.fetchVariant().then();
        },
      );
    },
  };

  fetchPriceList = async () => {
    mainStore.dispatch(
      fetchPriceList({
        cityGroup: this.state.selectedCityGroup!,
        variantCode: this.state.selectedVariant!.variant_code,
        modelName: this.state.selectedModel!.model_name,
      }) as any,
    );
  };

  fetchPromoCodes = async () => {
    const currentState: States = Object.assign({}, { ...this.state });
    try {
      const getPromoCodes = await promoService.getPromo({
        vehicle_brand: 'honda',
        vehicle_model: this.state.selectedModel?.model_name || '',
        vehicle_variant: this.state.selectedVariant?.variant_code || '',
        city_group: this.state.selectedCityGroup || '',
      });

      currentState.availablePromoCodes = getPromoCodes?.data || [];
    } catch (e) {
      currentState.availablePromoCodes = [];
    }

    this.setState(currentState);
  };

  discountRadioOnChange = (event: React.FormEvent<HTMLInputElement>, data: CheckboxProps) => {
    mainStore.dispatch(modalPriceListSliceSimple.actions.setDiscountType(data.value));
    if (data.value === 'promoCode') {
      this.fetchPromoCodes();
    }
  };

  onPromoCodeChange = (event: React.SyntheticEvent<HTMLElement>, data: DropdownProps) => {
    const promoCode = this.state.availablePromoCodes.find((p) => p.promo_code === data.value);
    mainStore.dispatch(modalPriceListSliceSimple.actions.setPromoCode(promoCode || null));
  };

  nominalDiscountChange = (event: React.ChangeEvent<HTMLInputElement>, data: InputOnChangeData) => {
    mainStore.dispatch(
      modalPriceListSliceSimple.actions.setNominalPromo(data.value ? parseInt(data.value) : null),
    );
  };

  finalDiscountDp = () => {
    let discountDp = 0;

    if (this.props.priceList.promoType !== 'none') {
      const otr = this.props.priceList.vehicle?.variant?.price || 0;
      switch (this.props.priceList.promoType) {
        case 'promoCode':
          const promoCode = this.props.priceList.selectedPromoCode;
          if (promoCode) {
            switch (promoCode.discount_type) {
              case 'nominal':
                discountDp = promoCode.discount_value;
                break;
              case 'percent':
                discountDp = (otr * promoCode.discount_value) / 100;
                break;
            }
          }
          break;
        case 'nominalInput':
          discountDp = this.props.priceList.nominalInputDiscount || 0;
          break;
      }
    }
    return discountDp;
  };

  componentDidMount() {
    const area = this.props.customer.client?.profile.area;
    if (area) {
      this.setState(
        {
          selectedCityGroup: area.value,
        },
        () => {
          const modelName = this.props.customer.client?.dream_vehicle?.model_name;
          if (modelName) {
            this.selectModel.fetchModel().then(() => {
              this.setState(
                {
                  selectedModel:
                    this.state.models.find(
                      (m) => m.model_name.toUpperCase() === modelName.toUpperCase(),
                    ) || null,
                },
                () => {
                  const variantCode = this.props.customer.client?.dream_vehicle?.variant_code;
                  if (variantCode) {
                    this.selectVariant.fetchVariant().then(() => {
                      const variant =
                        this.state.variants.find(
                          (v) => v.variant_code.toUpperCase() === variantCode.toUpperCase(),
                        ) || null;
                      mainStore.dispatch(modalPriceListSliceSimple.actions.reset());
                      mainStore.dispatch(
                        modalPriceListSliceSimple.actions.setVehicle({
                          cityGroup: this.state.selectedCityGroup!,
                          variant: variant!,
                        }),
                      );
                      this.setState(
                        {
                          selectedVariant: variant,
                        },
                        () => {
                          this.fetchPriceList();
                        },
                      );
                    });
                  } else {
                    this.selectVariant.fetchVariant().then();
                  }
                },
              );
            });
          } else {
            this.selectModel.fetchModel().catch();
          }
        },
      );
    }
  }

  renderTotalSelected = () => {
    return Object.values(this.props.priceList.selected).length;
  };

  disableSendButton = () => {
    if (this.state.sending) return true;
    if (this.renderTotalSelected() <= 0) return true;
  };

  onSend = async () => {
    this.setState({
      sending: true,
    });

    const selectedPricing = Object.values(this.props.priceList.selected);

    const availableDownPayment = collect(selectedPricing)
      .groupBy('downPayment')
      .keys()
      .map((v) => parseInt(v))
      .toArray<number>();
    const availableTenor = collect(selectedPricing)
      .groupBy('tenor')
      .keys()
      .map((v) => parseInt(v))
      .toArray<number>();

    let messages = `🚗 Berikut adalah pricelist untuk *${
      this.state.selectedVariant?.variant_name || ''
    }*
💰 OTR: ${currencyFormat(this.state.selectedVariant?.price || 0)}

`;
    availableDownPayment.forEach((dp) => {
      messages += `💳 *Uang muka ${currencyFormat(dp)}*\n`;
      if (this.props.priceList.promoType !== 'none') {
        messages += `🎁 Diskon Uang muka ${currencyFormat(this.finalDiscountDp())}\n`;
      }
      for (const tenor of availableTenor) {
        const find = selectedPricing.find((p) => p.downPayment === dp && p.tenor === tenor);
        if (!find) continue;
        messages += `📅 Tenor ${tenor} kali, 💸 angsuran ${currencyFormat(find.installment)}\n`;
      }
      messages += '\n';
    });

    messages = messages.replace(/\n/g, '<br/>');

    try {
      await mainApiServices.sendMessageV2({
        roomPath: this.props.conversation.chatRoom?.ref.path || '',
        phoneNumber: this.props.customer.client?.contacts.whatsapp || '',
        text: messages,
        adminSessionPath: this.props.admin.adminSession!.ref.path,
      });

      await updateDoc(this.props.customer.ref!, {
        pricelist: {
          sent: true,
          pricelists: Object.values(this.props.priceList.selected).map((p) => {
            return {
              tenor: p.tenor,
              installment: p.installment,
              down_payment: p.downPayment,
            };
          }),
          updatedAt: Timestamp.now(),
        },
      });

      await mainApiServices.logSendPriceList({
        event: 'sendPricelist',
        phone_number: this.props.customer.client?.contacts.whatsapp || '',
        name: this.props.customer.client?.profile.name || '',
        city_group: this.props.customer.client?.profile.area?.text || '',
        vehicle: {
          model_name: this.state.selectedModel?.model_name || '',
          variant_name: this.state.selectedVariant?.variant_name || '',
          variant_code: this.state.selectedVariant?.variant_code || '',
          variant_color_code: null,
          variant_color_name: null,
        },
        discount_promo: this.finalDiscountDp(),
        otr: this.state.selectedVariant?.price || 0,
        pricelists: Object.values(this.props.priceList.selected).map((p) => {
          return {
            tenor: p.tenor,
            installment: p.installment,
            down_payment: p.downPayment,
          };
        }),
        admin_id: this.props.admin.admin?.email || '',
        credit: {
          offer_code: null,
          down_payment: null,
          discount_down_payment: null,
          tenor: null,
          discount_tenor: null,
          installment: null,
          discount_installment: null,
        },
      });

      mainStore.dispatch(modalPriceListSliceSimple.actions.close());
    } catch (e) {
      this.setState({
        sending: false,
        errorMessage: 'Terjadi error pada saat mengirim link.',
      });

      return;
    }
  };

  render() {
    return (
      <Modal
        open={this.props.priceList.open}
        onClose={this.onCancel}
      >
        <Modal.Header>Kirim Price List</Modal.Header>

        {this.props.project.project?.group !== 'amartamotor' && (
          <Modal.Content>
            <Segment>
              <h3 className="text-red-800 font-medium mb-2">
                Project ini tidak bisa mengakses menu ini
              </h3>
              <p className="text-red-700 text-sm">
                Menu ini hanya tersedia untuk project Amarta Motor.
              </p>
            </Segment>
          </Modal.Content>
        )}

        {this.props.project.project?.group === 'amartamotor' && (
          <>
            <Modal.Content scrolling={true}>
              <Segment>
                <Form size={'small'}>
                  <Form.Group widths={'equal'}>
                    <Form.Field required={true}>
                      <label>City Group</label>
                      <SelectAreaFromCatalog
                        onChange={this.selectArea.onSelect}
                        value={this.state.selectedCityGroup ?? undefined}
                      />
                    </Form.Field>
                    <Form.Field required={true}>
                      <label>Model</label>
                      <Select
                        loading={this.state.fetchingModel}
                        placeholder={'Pilih model'}
                        multiple={false}
                        value={this.state.selectedModel?.model_name || ''}
                        onChange={this.selectModel.onSelect}
                        options={this.state.models.map((value) => ({
                          key: value.model_name,
                          value: value.model_name,
                          text: value.model_name.toUpperCase(),
                        }))}
                        search={true}
                        disabled={!this.state.selectedCityGroup}
                      />
                    </Form.Field>
                    <Form.Field required={true}>
                      <label>Pilih Variant</label>
                      <Select
                        placeholder={'Pilih Variant'}
                        multiple={false}
                        onChange={this.selectVariant.onSelect}
                        options={this.state.variants.map((value) => ({
                          key: value.variant_code,
                          value: value.variant_code,
                          text: value.variant_name.toUpperCase(),
                        }))}
                        search={true}
                        disabled={!this.state.selectedModel}
                        value={this.state.selectedVariant?.variant_code || ''}
                      />
                    </Form.Field>
                  </Form.Group>
                </Form>

                <div
                  style={{
                    width: '300px',
                    marginTop: '16px',
                  }}
                >
                  {this.props.priceList.priceListState === 'fetching' && (
                    <div>
                      <Icon
                        name={'spinner'}
                        loading={true}
                      />{' '}
                      Memeriksa ketersediaan
                    </div>
                  )}
                  {this.props.priceList.priceListState === 'not_found' && (
                    <div
                      style={{
                        color: '#B03060',
                        fontWeight: 'bold',
                      }}
                    >
                      <Icon name={'close'} /> Pricelist tidak tersedia
                    </div>
                  )}
                  {this.props.priceList.priceListState === 'available' && (
                    <div
                      style={{
                        display: 'flex',
                        gap: 6,
                        alignItems: 'center',
                      }}
                    >
                      <div
                        style={{
                          color: '#016936',
                          fontWeight: 'bold',
                        }}
                      >
                        <Icon name={'check'} /> Pricelist tersedia
                      </div>
                    </div>
                  )}
                </div>
              </Segment>
              <Segment>
                <div style={{ marginBottom: '16px' }}>
                  <strong>Potongan DP</strong>
                </div>
                <Form>
                  <Form.Group>
                    <Form.Field>
                      <Radio
                        label="Tanpa Potongan"
                        name="discountRadioGroupType"
                        value="none"
                        checked={this.props.priceList.promoType === 'none'}
                        onChange={this.discountRadioOnChange}
                      />
                    </Form.Field>
                    <Form.Field>
                      <Radio
                        label="Kode Promo"
                        name="discountRadioGroupType"
                        value="promoCode"
                        checked={this.props.priceList.promoType === 'promoCode'}
                        onChange={this.discountRadioOnChange}
                      />
                    </Form.Field>
                    <Form.Field>
                      <Radio
                        label="Nominal"
                        name="discountRadioGroupType"
                        value="nominalInput"
                        checked={this.props.priceList.promoType === 'nominalInput'}
                        onChange={this.discountRadioOnChange}
                      />
                    </Form.Field>
                  </Form.Group>
                  {this.props.priceList.promoType === 'promoCode' && (
                    <Form.Field>
                      <label>Kode Promo</label>
                      <Select
                        options={this.state.availablePromoCodes.map((p) => {
                          return {
                            value: p.promo_code,
                            text: p.promo_code,
                          };
                        })}
                        onChange={this.onPromoCodeChange}
                        value={this.props.priceList.selectedPromoCode?.promo_code || ''}
                      />
                    </Form.Field>
                  )}

                  {this.props.priceList.promoType === 'nominalInput' && (
                    <Form.Field>
                      <label>Masukan Nominal Potongan</label>
                      <Input
                        type={'number'}
                        value={this.props.priceList.nominalInputDiscount || ''}
                        onChange={this.nominalDiscountChange}
                      />
                    </Form.Field>
                  )}
                  <Form.Field>
                    <label>Potongan Akhir</label>
                    <span>{currencyFormat(this.finalDiscountDp())}</span>
                  </Form.Field>
                </Form>
              </Segment>
              <PriceListTable />

              {this.state.errorMessage && (
                <Message error={true}>
                  <pre>{this.state.errorMessage}</pre>
                </Message>
              )}
            </Modal.Content>
          </>
        )}
        <Modal.Actions>
          {this.props.priceList.priceListState === 'available' && (
            <Button
              primary={true}
              onClick={this.onSend}
              disabled={this.disableSendButton()}
              loading={this.state.sending}
            >
              Kirim ({this.renderTotalSelected()} dipilih)
            </Button>
          )}
          <Button onClick={this.onCancel}>Batal</Button>
        </Modal.Actions>
      </Modal>
    );
  }
}

const mapStateToProps = (states: TMainReduxStates) => ({
  conversation: states.reducerConversation,
  customer: states.customerReducer,
  priceList: states.modalSendPriceListSimple,
  admin: states.reducerAdmin,
  project: states.reducerProject,
});

const con = connect(mapStateToProps)(ModalSendPriceListSimple);

export default con;
