/*
 * ActiveConversation Component
 * This component renders the active conversation interface including client information, conversation flow, and chat input.
 * It adapts its layout for mobile/tablet devices and utilizes Redux for state management and data flow via hooks.
 */

/**
 * Komponen ActiveConversation.
 * Komponen ini menampilkan tampilan percakapan aktif, termasuk informasi klien, alur percakapan, dan input chat.
 * Tampilan disesuaikan untuk perangkat mobile/tablet dan memanfaatkan Redux untuk manajemen state serta alur data via hooks.
 *
 * @component
 * @example
 * return <ActiveConversation />;
 */

import React, { useCallback, useEffect, useMemo } from 'react'; // Import React and hooks
import { Button, Icon, Popup } from 'semantic-ui-react'; // Import UI components
import ConversationsBox from '../ChatBox/ConversationsBox'; // Component to display conversation list
import ChatTextInput from '../ChatBox/ChatTextInput/ChatTextInput'; // Component for chat text input
import { TMainReduxStates } from '../../redux/types/redux-types'; // Type definition for main redux states
import customizeMediaQuery, { CustomizeMediaQuery } from '../hoc/CustomizeMediaQuery'; // Higher order component for media queries
import { useDispatch, useSelector } from 'react-redux'; // Import hooks for Redux
import { openInfoSegment } from '../../redux/info-client-segment/action-info-client-segment'; // Action to open client info segment
import chatRoomSlice, { stopListenChatRoomMessages } from '../../redux/conversations/chatRoomSlice'; // Redux slice for chat room
import { stopConversationFlow as stopConversationFlowThunk } from '../../redux/conversations/chatRoomSlice'; // Action to stop conversation flow
import { IoHardwareChipOutline } from 'react-icons/io5';

// External props for ActiveConversation. Consumers of this component do not need to provide any props.
interface IActiveConversationExternalProps {}

// Internal props are a combination of external props and the injected mediaQueries
type IActiveConversationProps = IActiveConversationExternalProps & CustomizeMediaQuery;

// Functional component for ActiveConversation using Redux hooks
const ActiveConversation: React.FC<IActiveConversationProps> = (props) => {
  // Destructure mediaQueries from props
  const { mediaQueries } = props;
  const { isTabletOrMobile } = mediaQueries; // Determine if device is tablet/mobile

  // Retrieve redux state using useSelector hook
  const conversations = useSelector((state: TMainReduxStates) => state.reducerConversation);
  const clientInfoSegment = useSelector(
    (state: TMainReduxStates) => state.reducerInfoClientSegment,
  );
  const customer = useSelector((state: TMainReduxStates) => state.customerReducer);

  // Get dispatch function from useDispatch hook
  const dispatch = useDispatch<any>();

  /**
   * Fungsi untuk menghapus percakapan aktif.
   * Menghapus chat room dan pesan dari state.
   */
  // Define functions to dispatch actions
  const removeConversation = useCallback(() => {
    dispatch(stopListenChatRoomMessages());
  }, [dispatch]);

  /**
   * Fungsi untuk menghentikan alur percakapan aktif.
   * Mengirimkan aksi stopConversationFlowThunk melalui dispatch.
   */
  const stopConversationFlow = useCallback(() => {
    dispatch(stopConversationFlowThunk(conversations.chatRoom!));
  }, [dispatch, conversations.chatRoom]);

  /**
   * Fungsi untuk membuka segmen informasi klien.
   * Mengirimkan aksi openInfoSegment melalui dispatch.
   */
  const openInfoClientSegment = useCallback(() => {
    dispatch(openInfoSegment());
  }, [dispatch]);

  const { visible } = clientInfoSegment; // Visibility state of client info segment

  /**
   * Menentukan apakah tampilan percakapan mobile harus ditampilkan.
   * Berdasarkan perangkat mobile/tablet, visibilitas segmen informasi, dan adanya chat room.
   */
  // Determine whether to show the mobile chat view using memoization
  const shouldShowMobileChat = useMemo(() => {
    return isTabletOrMobile && !visible && !!conversations.chatRoom;
  }, [isTabletOrMobile, visible, conversations.chatRoom]);

  /**
   * Menghasilkan header yang menampilkan informasi klien.
   * Menampilkan nama klien dan, jika ada, informasi area serta kendaraan impian.
   */
  const renderClientInfoHeader = useCallback(() => {
    const { client } = customer;
    const hasArea = client?.profile.area; // Check if client's area exists
    const hasDreamVehicle = client?.dream_vehicle; // Check if client's dream vehicle exists
    const agentAiReply = conversations.chatRoom?.agent_ai_reply;

    return (
      <div className="flex flex-col justify-center min-h-[36px]">
        {/* Display client name */}
        <div className="flex items-center gap-2">
          <div className="font-semibold text-[15px] text-gray-800">
            {client?.profile.name || client?.profile.temporary_name || client?.contacts.whatsapp}
          </div>
          {agentAiReply && (
            <div className="flex items-center">
              <div className="bg-gradient-to-r from-teal-500 to-blue-500 text-white px-2.5 py-1 rounded-full flex items-center gap-1.5 text-xs font-medium shadow-sm animate-pulse">
                <IoHardwareChipOutline className="text-sm" />
                <span>AI Aktif</span>
              </div>
            </div>
          )}
        </div>
        {/* Conditionally render client area and dream vehicle if available */}
        {(hasArea || hasDreamVehicle) && (
          <div className="flex flex-wrap gap-1.5">
            {hasArea && (
              <span className="text-[11px] leading-none bg-blue-50 text-blue-600 px-1.5 py-1 rounded-md">
                {client.profile.area?.text}
              </span>
            )}
            {hasDreamVehicle && (
              <span className="text-[11px] leading-none bg-green-50 text-green-600 px-1.5 py-1 rounded-md">
                {client.dream_vehicle?.model_name}
              </span>
            )}
          </div>
        )}
      </div>
    );
  }, [customer, conversations]);

  /**
   * Menghasilkan header untuk tampilan mobile.
   * Menyertakan tombol navigasi dan informasi klien.
   */
  const renderMobileHeader = useCallback(() => {
    return (
      <div className="header-active-conversation bg-white border-b border-gray-200">
        <div className="h-[60px] flex items-center justify-between px-4">
          {/* Back button to remove conversation */}
          <Button
            icon
            basic
            className="!p-1.5 !bg-transparent hover:!bg-gray-50"
            onClick={removeConversation}
          >
            <Icon name="arrow left" />
          </Button>
          <div className="flex-1 mx-3">
            {/* Render client info header */}
            {renderClientInfoHeader()}
          </div>
          {/* Info button to open client info segment */}
          <Button
            icon
            basic
            className="!p-1.5 !bg-transparent hover:!bg-gray-50"
            onClick={openInfoClientSegment}
          >
            <Icon name="info circle" />
          </Button>
        </div>
      </div>
    );
  }, [removeConversation, openInfoClientSegment, renderClientInfoHeader]);

  useEffect(() => {
    return () => {
      removeConversation();
    };
  }, []);

  /**
   * Menghasilkan konten utama percakapan.
   * Jika tidak ada percakapan aktif, tampilkan placeholder.
   * Jika percakapan aktif ada, tampilkan interface chat dengan kontrol alur percakapan.
   */
  const renderMainContent = useCallback(() => {
    if (!conversations.chatRoom) {
      // If there is no active chat, render a placeholder
      return (
        <div className="flex flex-col items-center justify-center h-full bg-gray-50 p-8">
          <div className="bg-white rounded-2xl shadow-sm p-8 flex flex-col items-center max-w-md w-full">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              {/* Icon indicating no conversation */}
              <Icon
                name="comments"
                size="large"
                className="text-blue-600"
              />
            </div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">Belum ada percakapan</h3>
            <p className="text-gray-500 text-center">
              Pilih percakapan dari daftar untuk memulai chat
            </p>
          </div>
        </div>
      );
    }

    // If an active chat exists, render the conversation interface
    return (
      <>
        <div className="header-active-conversation bg-white border-b border-gray-200">
          <div className="h-[60px] flex items-center justify-between px-4">
            {/* Display client info header */}
            {renderClientInfoHeader()}
            {/* Button to close conversation */}
            <Button
              basic
              size="tiny"
              className="!bg-transparent hover:!bg-gray-50 !text-gray-600 !p-1.5"
              onClick={removeConversation}
            >
              <Icon
                name="close"
                className="!m-0"
              />
            </Button>
          </div>
        </div>
        {/* Section for pinned message display */}
        {conversations.chatRoom?.pinnedMessages && conversations.chatRoom.pinnedMessages.length > 0 && (
          <div className="w-full">
            <div className="bg-blue-50 p-4 shadow flex flex-col">
              <div className="flex items-center gap-2 mb-2">
                <Icon name="pin" className="text-blue-600" />
                <h4 className="text-sm font-semibold text-blue-800">
                  Pesan Terpin
                </h4>
              </div>
              <div className="bg-white rounded-lg p-3 border border-blue-200">
                  <p className="text-sm text-gray-700">
                    {conversations.chatRoom.pinnedMessages[0].message.text?.body || 'Pesan tidak tersedia'}
                  </p>
                </div>
            </div>
          </div>
        )}
        {/* Render conversation list and chat text input */}
        <ConversationsBox />
        <ChatTextInput />
      </>
    );
  }, [conversations, removeConversation, renderClientInfoHeader]);

  // Conditionally render based on device type and conversation state
  if (shouldShowMobileChat) {
    // Mobile view: show header, conversation list, and chat input
    return (
      <div className="flex-auto flex flex-col h-full bg-gray-50">
        {renderMobileHeader()}
        <ConversationsBox />
        <ChatTextInput />
      </div>
    );
  }
  // For mobile/tablet devices without active conversation, render nothing
  if (isTabletOrMobile) return null;
  // Desktop view: render the main conversation content
  return <div className="flex-1 flex flex-col h-full bg-gray-50">{renderMainContent()}</div>;
};

// Export the component with the media query HOC applied
// The HOC injects the mediaQueries prop, so external consumers do not need to provide it.
export default customizeMediaQuery<any>(ActiveConversation);
